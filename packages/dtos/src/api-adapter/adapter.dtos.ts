import { Expose, Transform } from "class-transformer";
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from "class-validator";
import { RequestModuleEnum } from "../../../enums/src";
import { CurrentUser } from "@types";

export class ApiAdapterRequestOptionsDto {
  @Expose()
  @IsOptional()
  idempotencyKey?: string;

  @Expose()
  @IsOptional()
  language?: string;
}

export class ApiAdapterRequestDto {
  @Expose()
  @IsNotEmpty()
  requestId: string;

  @Expose()
  @IsNotEmpty()
  @IsEnum(RequestModuleEnum)
  module: RequestModuleEnum;

  @Expose()
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value ?? "v1")
  version: string = "v1";

  @Expose()
  @IsOptional()
  payload: any;

  @Expose()
  @IsOptional()
  user: CurrentUser;

  @Expose()
  @IsOptional()
  options: ApiAdapterRequestOptionsDto;
}
