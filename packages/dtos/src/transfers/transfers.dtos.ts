import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsString,
} from "class-validator";
import { AttachmentRefDto } from "../common";
import { TransfersChargeOptionEnum } from "@enums";
import { IsFccDateFormat } from "@helpers";

export class TransferSubmitRequestDto {
  @IsString()
  @IsNotEmpty()
  applicantActName: string;

  @IsString()
  @IsNotEmpty()
  applicantActNo: string;

  @IsString()
  @IsNotEmpty()
  applicantActCurCode: string;

  @IsString()
  @IsOptional()
  applicantActDescription: string;

  @IsNumberString()
  @IsNotEmpty()
  ftAmt: string;

  @IsString()
  @IsNotEmpty()
  ftCurCode: string;

  @IsString()
  @IsOptional()
  paymentDetailsFt: string;

  @IsString()
  @IsOptional()
  reauthPassword: string;

  @IsOptional()
  @IsNotEmpty()
  @IsFccDateFormat()
  transferDate: string;

  @IsOptional()
  @IsArray()
  attachments: string[];
}

export class SwiftTransferSubmitRequestDto extends TransferSubmitRequestDto {
  @IsString()
  @IsNotEmpty()
  beneficiaryName: string;

  @IsString()
  @IsNotEmpty()
  beneficiaryAccount: string;

  @IsEnum(TransfersChargeOptionEnum)
  @IsNotEmpty()
  chargeOption: TransfersChargeOptionEnum;
}
