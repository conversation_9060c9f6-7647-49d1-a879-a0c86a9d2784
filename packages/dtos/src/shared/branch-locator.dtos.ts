import { Expose, Transform, Type } from "class-transformer";
import {
  IsBoolean,
  IsOptional,
  Max,
  MaxLength,
  Min,
  MinLength,
} from "class-validator";

export class GetBranchsLocationsDto {
  @Expose()
  @IsOptional()
  lng: number;

  @Expose()
  @IsOptional()
  lat: number;

  @Expose()
  @IsOptional()
  standAloneATMs: boolean;

  @Expose()
  @IsOptional()
  branchesWithATMs: boolean;

  @Expose()
  @IsOptional()
  branch: boolean;

  @Expose()
  @IsOptional()
  deposit: boolean;

  @Expose()
  @IsOptional()
  withdrawal: boolean;

  @Expose()
  @IsOptional()
  chequeDeposit: boolean;

  @Expose()
  @IsOptional()
  forex: boolean;

  @Expose()
  @IsOptional()
  talkingATM: boolean;

  @Expose()
  @IsOptional()
  specialNeedsBathroom: boolean;

  @Expose()
  @IsOptional()
  specialNeedsRamp: boolean;

  @Expose()
  @IsOptional()
  specialNeedsTeller: boolean;

  @Expose()
  @IsOptional()
  businessBanking: boolean;

  @Expose()
  @IsOptional()
  plus: boolean;

  @Expose()
  @IsOptional()
  wealth: boolean;

  @Expose()
  @IsOptional()
  prime: boolean;

  @Expose()
  @IsOptional()
  corporate: boolean;

  @Expose()
  @IsOptional()
  private: boolean;

  @Expose()
  @IsOptional()
  @MinLength(3)
  @MaxLength(150)
  query: string;

  @Expose()
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => value ?? 1)
  @Min(1)
  page: number;

  @Expose()
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  @Max(20)
  @Transform(({ value }) => value ?? 20)
  limit: number;
}
