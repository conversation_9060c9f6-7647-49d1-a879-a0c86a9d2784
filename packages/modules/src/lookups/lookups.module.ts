import { DynamicModule, Module, Global, Logger } from "@nestjs/common";
import { ScheduleModule } from "@nestjs/schedule";

import { HttpModule } from "@nestjs/axios";
import { LookupsEntityOptions } from "./lookups.types";
import { LookupsRemoteDataService } from "./remote-data.service";
import { I18nLookupService } from "./services/i18n.service";
import { DynamicListsLookupService, LazyResourcesLookupService } from "./services";

@Global()
@Module({})
export class LookupsModule {
  static forRoot(entities: LookupsEntityOptions[] = []): DynamicModule {
    return {
      module: LookupsModule,
      imports: [ScheduleModule.forRoot(), HttpModule],
      providers: [
        {
          provide: "entities",
          useValue: entities,
        },
        LookupsRemoteDataService,
        I18nLookupService,
        LazyResourcesLookupService,
        DynamicListsLookupService,
        Logger,
      ],
      exports: [
        LookupsRemoteDataService,
        I18nLookupService,
        LazyResourcesLookupService,
        DynamicListsLookupService
      ],
    };
  }
}
