export enum RequestModuleEnum {
  ESB = "esb",
  FCC = "fcc",
  CIBEG = "cibeg",
}

export enum FccRequestsEnum {
  MY_PENDING_TRANSACTIONS = "my-pending-transactions",
  REJECT_TRANSACTION = "reject-transaction",
  APPROVE_TRANSACTION = "approve-transaction",
  TRANSACTION_DETAILS = "transaction-details",
  TRANSACTION_JOURNEY_DETAILS = "transaction-journey",
  TRANSACTION_HISTORY = "transaction-history",

  USER_PERMISSIONS = "user-permissions",
  USER_DETAILS = "user-details",
  USER_REAUTHENTICATION_TYPE = "user-reauthentication-type",

  UPLOAD_ATTACHMENT = "upload-attachment",
  DELETE_ATTACHMENT = "delete-attachment",
  GET_ATTACHMENT_LIST = "get-attachment-list",

  BANKS_LIST = "banks-list",
  GENERIC_AUTH_TYPE = "generic-auth-type",
  CONFIGURATION_DETAILS = "get-configuration-details",

  INTERNAL_TRANSFER_INITIATE = "internal-transfer-initiate",
  INTERNAL_TRANSFER_SUBMIT = "internal-transfer-submit",
  LOCAL_TRANSFER_INITIATE = "tpt-transfer-initiate",
  LOCAL_TRANSFER_SUBMIT = "tpt-transfer-submit",

  SWIFT_TRANSFER_INITIATE = "swift-transfer-initiate",
  SWIFT_TRANSFER_SUBMIT = "swift-transfer-submit",

  TRANSFERS_BENEFICIARIES_LIST = "transfers-beneficiaries-list",
  TRANSFERS_BENEFICIARIES_DELETE = "transfers_beneficiaries_delete",
  TRANSFERS_BENEFICIARIES_CREATE_TPT = "transfers-beneficiaries-create-tpt",
  TRANSFERS_BENEFICIARIES_UPDATE_TPT = "transfers-beneficiaries-update-tpt",
  TRANSFERS_BENEFICIARIES_CREATE_MT103 = "transfers-beneficiaries-create-mt103",
  TRANSFERS_BENEFICIARIES_UPDATE_MT103 = "transfers-beneficiaries-update-mt103",

  COUNTRY_LIST = "country-list",
  CURRENCY_LIST = "currency-list",

  BANKS_SWIFT_DETAILS = "swift-details",
  CLEARING_SYSTEM = "clearing-system",

  ACCOUNTS_LIST = "accounts-list",
  ACCOUNTS_HOME_SUMMARY = "accounts-home-summary",
  ACCOUNTS_BALANCE_SUMMARY = "accounts-balance-summary",

  MUTUAL_FUND_SUMMARY = "mutual-fund-summary",

  LOANS_SUMMARY = "loans-summary",


  FX_CONVERT = "fx-convert",
  FX_RATE = "fx-rate",
  SECURE_MAIL_INITIATE = "secure-mail-initiate",
  SECURE_MAIL_SUBMIT = "secure-mail-submit",
}

export enum EsbRequestsEnum {
  CUSTOMER_DETAILS = "customer-details",
  ACCOUNT_DETAILS = "account-details",
}

export enum CibegRequestsEnum {
  BRANCH_LOCATIONS = "branch-locations",
  BRANCH_LOCATION_DETAILS = "branch-location-details",
}
