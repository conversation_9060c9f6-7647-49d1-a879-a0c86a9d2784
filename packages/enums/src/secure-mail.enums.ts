export enum SecureMailTypesEnum {
  SUPPORTING_DOCUMENTS = "Supporting Documents",
  // EKYC_FORM_UPLOAD = "EKYC Form Upload",
  // ADD_NEW_CUSTOMER_NUMBER = "Add New Customer Number",
  STOP_MI_PAYMENTS = "Stop mi Payments",
  ADD_NEW_PERMISSION = "Add New Permission",
  // TOKEN_REQUEST = "Token Request",
  DELETE_USERS = "Delete Users",
  // IMPORT_COLLECTION = "Import Collection",
  // EXPORT_COLLECTION = "Export Collection",
  // BANKERS_GUARANTEE = "Banker's Guarantee",
  // SHIPPING_GUARANTEE = "Shipping Guarantee",
  // EXPORT_LETTER_OF_CREDIT = "Export Letter of Credit",
  // IMPORT_LETTER_OF_CREDIT = "Import Letter of Credit",
  // ADDING_NEW_BENEFICIARY = "Adding New Beneficiary",
  // ONLINE_BOOKING_ESCF = "Online Booking / eSCF",
  ASSIGN_ACCOUNT = "Assign Account",
  // MERGE_PROFILE = "Merge profile",
  CANCEL_BANK_DRAFT = "Cancel bank draft",
  CPS_CHARGE_BACK_REQUEST = "CPS charge back request",
  CPS_AMEND_ADD_SERVICE = "CPS Amend / Add service",
  // TD_BREAK = "TD break",
  // ISSUE_CERTIFICATE_SHIPPING = "Issue Certificate ( Shipping )",
  CANCEL_SECURE_EMAIL = "Cancel Secure email",
  CHANGE_USER_PRIVILEGES = "Change user privileges",
  // AMEND_BENEFICIARY = "Amend Beneficiary",
  ADD_NEW_USER = "Add New User",
}

export enum SecureMailDynamicListEnum {
  USER_PRIVILEGES = "USER_PRIVILEGES",
  PORTAL_TYPES = "PORTAL_TYPES",
}
