import { Module } from '@nestjs/common';
import {
  ApiAdapterClient,
  GlobalConfigModule,
  JwtAuthModule,
  LookupEntity,
  LookupsModule,
  RedisModule,
} from '@modules';
import defaults from './config/defaults';
import { AppContentModule } from './modules/app-content/app-content.module';
import { AuthTypeModule } from '@/modules/auth-type/auth-type.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { StaticDataModule } from './modules/static-data/static-data.module';
import { BranchLocatorModule } from './modules/branch-locator/locator.module';

@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    RedisModule.forRootAsync(),
    JwtAuthModule.forRoot(),
    ApiAdapterClient,
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
    ]),
    AppContentModule,
    DocumentsModule,
    AuthTypeModule,
    StaticDataModule,
    BranchLocatorModule
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
