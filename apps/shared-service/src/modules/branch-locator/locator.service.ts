import { GetBranchsLocationsDto } from '@dtos';
import { CibegRequestsEnum, RequestModuleEnum, UserLanguage } from '@enums';
import { sortByDistance } from '@helpers';
import { ApiAdapterClientService } from '@modules';
import { Injectable } from '@nestjs/common';

@Injectable()
export class BranchLocatorService {
  constructor(private readonly apiAdapterService: ApiAdapterClientService) {}

  async getLocations(query: GetBranchsLocationsDto, language: UserLanguage) {
    const {
      result: { locations = [] },
    } = await this.apiAdapterService.call({
      module: RequestModuleEnum.CIBEG,
      requestId: CibegRequestsEnum.BRANCH_LOCATIONS,
      payload: query,
      options: {
        language,
      },
    });

    let results = [...locations];
    if (results.length) {
      if (query.lat && query.lng) {
        results = sortByDistance(locations, query.lat, query.lng);
      }
      results = this.applyPagination(locations, query.page, query.limit);
    }

    return {
      results,
      meta: {
        page: query.page,
        limit: query.limit,
        pageCount: Math.ceil(locations.length / query.limit),
        itemCount: results.length,
      },
    };
  }

  async getLocationDetails(mapid: string, language: UserLanguage) {
    const results = await this.apiAdapterService.call({
      module: RequestModuleEnum.CIBEG,
      requestId: CibegRequestsEnum.BRANCH_LOCATION_DETAILS,
      payload: {
        mapid,
      },
      options: {
        language,
      },
    });

    return results;
  }

  private applyPagination<T>(array: T[], page: number, limit: number): T[] {
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    return array.slice(startIndex, endIndex);
  }
}
