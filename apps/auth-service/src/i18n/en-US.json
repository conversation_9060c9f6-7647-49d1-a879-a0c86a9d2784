{"auth.login.success": "Authentication successful", "auth.login.invalid_credentials": "Your user or password is incorrect", "auth.login.locked_account": "Your account has been locked please contact call center", "auth.login.expired_password": "Your password is expired", "auth.login.active_session": "Active session detected. Termination required before login.", "auth.login.terms_required": "Terms and conditions acceptance required", "auth.login.token_error": "Failed to terminate session", "auth.login.system_error": "Authentication failed", "auth.login.session_terminated": "Session terminated and new login successful", "auth.login.logout_success": "Logged out successfully", "auth.password.reset_success": "Password reset successful", "auth.password.reset_failed": "Failed to reset password", "auth.password.mismatch": "Passwords do not match", "auth.password.complexity": "Password does not meet complexity requirements", "auth.token.missing": "Reset password token is missing", "auth.token.invalid": "Invalid reset password token", "auth.token.refresh_failed": "Failed to refresh token", "auth.token.refresh_success": "<PERSON><PERSON> refreshed successfully", "auth.terms.accepted": "Terms and conditions accepted successfully", "auth.terms.failed": "Failed to accept terms and conditions", "auth.forget_password.options_success": "Forget password options retrieved successfully", "auth.forget_password.options_failed": "Failed to retrieve forget password options", "auth.forget_password.captcha_required": "Captcha verification required", "auth.forget_password.invalid_captcha": "Invalid cap<PERSON><PERSON>, please try again", "auth.forget_password.user_not_found": "User not found"}