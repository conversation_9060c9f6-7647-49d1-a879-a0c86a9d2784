export const AUTH_MESSAGES = {
  LOGIN: {
    SUCCESS: 'auth.login.success',
    INVALID_CREDENTIALS: 'auth.login.invalid_credentials',
    LOCKED_ACCOUNT: 'auth.login.locked_account',
    EXPIRED_PASSWORD: 'auth.login.expired_password',
    ACTIVE_SESSION: 'auth.login.active_session',
    TERMS_REQUIRED: 'auth.login.terms_required',
    TOKEN_ERROR: 'auth.login.token_error',
    SYSTEM_ERROR: 'auth.login.system_error',
    SESSION_TERMINATED: 'auth.login.session_terminated',
    LOGOUT_SUCCESS: 'auth.login.logout_success',
    INVALID_CAPTCHA: 'auth.login.invalid_captcha',
    INVALID_CREDENTIALS_CAPTCHA: 'auth.login.invalid_credentials_valid_captcha',
    CAPTCHA_REQUIRED: 'auth.login.captcha_required',
  },
  PASSWORD: {
    RESET_SUCCESS: 'auth.password.reset_success',
    RESET_FAILED: 'auth.password.reset_failed',
    <PERSON><PERSON><PERSON><PERSON>: 'auth.password.mismatch',
    COMPLEXITY: 'auth.password.complexity',
  },
  TOKEN: {
    MISSING: 'auth.token.missing',
    INVALID: 'auth.token.invalid',
    REFRESH_FAILED: 'auth.token.refresh_failed',
    REFRESH_SUCCESS: 'auth.token.refresh_success',
  },
  TERMS: {
    ACCEPTED: 'auth.terms.accepted',
    FAILED: 'auth.terms.failed',
  },
  FORGET_PASSWORD: {
    OPTIONS_SUCCESS: 'auth.forget_password.options_success',
    OPTIONS_FAILED: 'auth.forget_password.options_failed',
    CAPTCHA_REQUIRED: 'auth.forget_password.captcha_required',
    INVALID_CAPTCHA: 'auth.forget_password.invalid_captcha',
    USER_NOT_FOUND: 'auth.forget_password.user_not_found',
  },
};
