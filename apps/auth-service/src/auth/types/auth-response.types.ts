export enum AuthResponseCode {
  SUCCESS = 'SUCCESS',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  LOCKED_ACCOUNT = 'LOCKED_ACCOUNT',
  EXPIRED_PASSWORD = 'EXPIRED_PASSWORD',
  ACTIVE_SESSION = 'ACTIVE_SESSION',
  TERMS_REQUIRED = 'TERMS_REQUIRED',
  TOKEN_ERROR = 'TOKEN_ERROR',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  CAPTCHA_REQUIRED = 'CAPTCHA_REQUIRED',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
}

export interface AuthResponseBase {
  responseCode: AuthResponseCode;
  message: string;
}

export interface LoginSuccessResponse extends AuthResponseBase {
  token: string;
  userData: any;
}

export interface TermsRequiredResponse extends AuthResponseBase {
  token: string;
  terms: string;
  tokenType: string;
}

export interface ActiveSessionResponse extends AuthResponseBase {
  terminationToken: string;
}

export interface PasswordResetResponse extends AuthResponseBase {
  resetPasswordToken: string;
}

export type LoginResponse =
  | LoginSuccessResponse
  | TermsRequiredResponse
  | ActiveSessionResponse
  | PasswordResetResponse
  | AuthResponseBase;
