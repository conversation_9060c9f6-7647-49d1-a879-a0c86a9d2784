import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { I18nLookupService, RedisCacheService } from '@modules';
import { ForgetPasswordOptionsDto } from '../dto/forget-password-options.dto';
import { CaptchaService } from './captcha.service';
import { AuthResponseCode } from '../types/auth-response.types';
import { AUTH_MESSAGES } from '../constants/auth-messages.constants';
import { buildCaptchaResponse, buildResponse } from '../utils/response.utils';
import { IsamService } from './isam.service';
import { StateIdParserService } from './stateIdParserService';
import { JSDOM } from 'jsdom';
import { UserLanguage } from '@enums';
import { JwtTokenService } from './jwt-token.service';
import { parseSetCookieHeaders } from '../utils/cookie.utils';

@Injectable()
export class ForgetPasswordService {
  private readonly logger: Logger;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nLookupService,
    private readonly redisCacheService: RedisCacheService,
    private readonly captchaService: CaptchaService,
    private readonly isamService: IsamService,
    private readonly stateIdParserService: StateIdParserService,
    private readonly jwtTokenService: JwtTokenService,
  ) {
    this.logger = new Logger(ForgetPasswordService.name);
  }

  /**
   * Get available forget password options for a user
   * @param forgetPasswordOptionsDto DTO containing username, company, and captcha info
   * @param language User's preferred language
   * @returns Available forget password options
   */
  async getForgetPasswordOptions(
    forgetPasswordOptionsDto: ForgetPasswordOptionsDto,
    language: UserLanguage = UserLanguage['en-US'],
  ) {
    const { username, company, captchaText, captchaToken } = forgetPasswordOptionsDto;

    // Validate the provided captcha
    const isValid = await this.captchaService.validateCaptcha(captchaToken, captchaText);
    if (!isValid) {
      // Generate new captcha for the response
      const { image, token } = await this.captchaService.generateCaptcha();
      return buildCaptchaResponse(
        this.i18nService,
        AuthResponseCode.CAPTCHA_REQUIRED,
        AUTH_MESSAGES.FORGET_PASSWORD.INVALID_CAPTCHA,
        language,
        image,
        token,
      );
    }

    try {
      // Initiate a forget password process
      const forgetPasswordResponse = await this.isamService.initiateForgetPassword(
        username,
        company,
        this.stateIdParserService,
      );

      // Log the headers for debugging
      this.logger.debug('Forget password response headers:', forgetPasswordResponse.headers);

      // Check if the response indicates user not found
      if (this.isUserNotFoundResponse(forgetPasswordResponse.body)) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.USER_NOT_FOUND,
          AUTH_MESSAGES.FORGET_PASSWORD.USER_NOT_FOUND,
          language,
        );
      }

      // Extract available options from the response
      const options = this.extractForgetPasswordOptions(forgetPasswordResponse.body);

      // Extract cookies from response headers
      const cookies = this.extractHeadersAndCookies(forgetPasswordResponse);
      const parsedCookies = parseSetCookieHeaders(cookies);

      // Extract stateId from the HTML response
      const stateId = this.stateIdParserService.parseStateIdFromHtml(forgetPasswordResponse.body);

      // Generate token with cookies and stateId
      const resetOptionsToken = await this.jwtTokenService.generateToken(parsedCookies, stateId);

      return {
        responseCode: AuthResponseCode.SUCCESS,
        message: this.i18nService.translate(
          AUTH_MESSAGES.FORGET_PASSWORD.OPTIONS_SUCCESS,
          language,
        ),
        options,
        resetOptionsToken,
      };
    } catch (error) {
      this.logger.error('Failed to get forget password options', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.FORGET_PASSWORD.OPTIONS_FAILED,
        language,
      );
    }
  }

  /**
   * Check if the response indicates user not found
   */
  private isUserNotFoundResponse(html: string): boolean {
    return html.includes('User not found') ||
      html.includes('Invalid username') ||
      html.includes('Invalid credentials');
  }

  /**
   * Extract available forget password options from the HTML response
   */
  private extractForgetPasswordOptions(html: string): any[] {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;
      const options = [];

      // Check for available reset options (radio buttons)
      const radioButtons = document.querySelectorAll('input[type="radio"][name="option"]');

      radioButtons.forEach(radio => {
        const value = radio.getAttribute('value');
        const parentDiv = radio.closest('.form-check');

        // Only include options that are not hidden by CSS or JavaScript
        if (parentDiv) {
          // Check if the option is visible (not hidden by display:none)
          const isVisible = parentDiv.style.display !== 'none';

          // Get the div ID to check against JavaScript variables
          const divId = parentDiv.getAttribute('id');

          // Extract JavaScript variable values from the HTML
          const jsVarRegex = new RegExp(`var\\s+${divId}\\s*=\\s*"([^"]*)"`, 'i');
          const jsVarMatch = html.match(jsVarRegex);

          // Check if the option is hidden by JavaScript
          // If the variable is "false" or if there's code to hide this element
          const scriptHidden = (jsVarMatch && jsVarMatch[1] === 'false') ||
            html.includes(`document.getElementById('${divId}').style.display = 'none'`);

          if (isVisible && !scriptHidden) {
            const label = parentDiv.querySelector('.form-check-label');
            const labelText = label ? label.textContent.trim() : '';

            options.push({
              type: value,
              label: labelText,
            });
          }
        }
      });

      // Placeholder implementation - in a real scenario, you would parse the HTML
      // to extract the available options (email, SMS, etc.)
      return options;
    } catch (error) {
      this.logger.error('Failed to extract forget password options', error);
      return [];
    }
  }

  /**
   * Extract headers and cookies from response
   */
  private extractHeadersAndCookies(response: any): string[] {
    return response.headers['set-cookie'] || [];
  }
}
