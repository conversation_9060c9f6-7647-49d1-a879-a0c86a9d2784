import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import * as https from 'https';
import { LoginDto } from '../dto/login.dto';
import { ResetPasswordDto } from '../dto/reset-password.dto';
import { JwtTokenService } from './jwt-token.service';
import { IsamService } from './isam.service';
import { StateIdParserService } from './stateIdParserService';
import { parseSetCookieHeaders } from '../utils/cookie.utils';
import { CurrentUser } from '@types';
import { ActiveSessionResponse, IsamErrorCodes } from '../types/isam.types';
import { AuthResponseCode } from '../types/auth-response.types';
import { AuthResponseDto } from '../dto/auth-response.dto';
import { AUTH_MESSAGES } from '../constants/auth-messages.constants';
import { I18nLookupService, RedisCacheService } from '@modules';
import { UserLanguage } from '@enums';
import { CaptchaService } from './captcha.service';
import { buildCaptchaResponse, buildResponse } from '../utils/response.utils';
import { UserDetailsService } from '@/auth/services/user-details.service';

@Injectable()
export class AuthService {
  // Constants for security and logic thresholds
  private readonly MAX_ATTEMPTS: number;
  private readonly CAPTCHA_THRESHOLD: number;
  private readonly ATTEMPT_TTL: number;

  private readonly agent;
  private readonly logger: Logger;

  readonly baseUrl: string;
  readonly loginBaseUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly isamService: IsamService,
    private readonly stateIdParserService: StateIdParserService,
    private readonly i18nService: I18nLookupService,
    private readonly redisCacheService: RedisCacheService,
    private readonly captchaService: CaptchaService,
    private readonly userDetailsService: UserDetailsService,
  ) {
    this.logger = new Logger(AuthService.name);
    this.agent = new https.Agent({ rejectUnauthorized: false });
    this.MAX_ATTEMPTS = this.configService.get<number>('AUTH_MAX_ATTEMPTS', 5);
    this.CAPTCHA_THRESHOLD = this.configService.get<number>('AUTH_CAPTCHA_THRESHOLD', 3);
    this.ATTEMPT_TTL = this.configService.get<number>('AUTH_ATTEMPT_TTL', 24 * 60 * 60); // 24 hours in seconds
    this.baseUrl = this.configService.getOrThrow('ISAM_BASE_URL');
    this.loginBaseUrl = this.configService.getOrThrow('ISAM_BASE_URL').replace(/\/FBCCBB$/, '');

  }

  /**
   * User login endpoint.
   * Handles both normal and termination login flows.
   */
  async login(
    loginDto: LoginDto,
    terminationHeader: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    if (terminationHeader) {
      return this.handleTermination(terminationHeader, language);
    }
    return this.handleNormalLogin(loginDto, language);
  }

  /**
   * Logout the current user.
   * Always returns success to the client, regardless of ISAM response.
   * As we want to clear their local session
   */
  async logout(currentUser: CurrentUser): Promise<AuthResponseDto> {
    this.logger.log('User logging out');
    try {
      const logoutUrl = `${this.loginBaseUrl}/pkmslogout`;
      // Extract cookies from current user
      const cookies = currentUser.cookies || '';
      // Call ISAM logout endpoint
      await lastValueFrom(
        this.httpService.get(logoutUrl, {
          headers: { Cookie: cookies },
          httpsAgent: this.agent,
        } as any),
      );
      this.logger.log('ISAM logout successful');
    } catch (error) {
      this.logger.error('ISAM logout failed', error);
    }
    return {
      responseCode: AuthResponseCode.SUCCESS,
      message: this.i18nService.translate(
        AUTH_MESSAGES.LOGIN.LOGOUT_SUCCESS,
        UserLanguage['en-US'],
      ),
    };
  }

  /**
   * Handle a development login process
   */
  async devLogin(loginDto: LoginDto): Promise<AuthResponseDto> {
    try {
      this.logger.log('Development login initiated');

      // Create custom payload with actual login credentials
      const customPayload = this.isamService.createRestPortalPayload({
        username: loginDto.username,
        company: loginDto.company,
        password: loginDto.password,
      });

      // Get dev login URL from config
      const devLoginUrl =
        this.configService.get<string>('FCC_URL') + '/restportal/login';

      // Call REST portal authentication with mock response, custom payload, and custom URL
      const isamRestPortalResponse =
        await this.isamService.performRestPortalAuthentication(
          {
            statusCode: '302',
            status: 302,
            headers: { 'set-cookie': [] },
          },
          customPayload,
          devLoginUrl,
        );

      // Generate a unique stateId for dev environment
      const stateId = `dev-${Date.now()}`;

      // Extract headers from response
      const isamRestPortalResponseHeaders = this.extractHeadersAndCookies(
        isamRestPortalResponse,
      );

      // Parse headers
      const parsedHeaders = parseSetCookieHeaders(
        isamRestPortalResponseHeaders,
      );

      // Generate JWT token
      const jwtToken = await this.jwtTokenService.generateToken(
        parsedHeaders,
        stateId,
      );

      // Return authentication result
      return {
        responseCode: AuthResponseCode.SUCCESS,
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.SUCCESS,
          UserLanguage['en-US'],
        ),
        token: jwtToken,
        userData: JSON.parse(isamRestPortalResponse.body),
      };
    } catch (error) {
      this.logger.error('Development login failed', error);
      return {
        responseCode: AuthResponseCode.INVALID_CREDENTIALS,
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.INVALID_CREDENTIALS,
          UserLanguage['en-US'],
        ),
      };
    }
  }

  /**
   * Refresh an existing JWT token
   */
  async refreshToken(
    authorizationHeader: string,
  ): Promise<{ token: string; nearExpiry: boolean }> {
    this.logger.log('Token refresh requested');
    if (!authorizationHeader) {
      throw new UnauthorizedException(
        'Authorization header is missing',
        'TOKEN_MISSING',
      );
    }
    try {
      // Extract token from authorization header
      const token = authorizationHeader.replace(/^Bearer\s+/i, '');

      // Generate a new token
      const newToken = await this.jwtTokenService.generateRefreshToken(token);

      // Check if the new token is near expiry (this would indicate ISAM session is about to expire)
      const nearExpiry = await this.jwtTokenService.isTokenNearExpiry(newToken);
      return { token: newToken, nearExpiry };
    } catch (error) {
      this.logger.error('Token refresh failed', error);
      if (error instanceof UnauthorizedException) throw error;
      throw new UnauthorizedException(
        'Failed to refresh token',
        IsamErrorCodes.INVALID_CREDENTIALS,
      );
    }
  }

  /**
   * Reset user password
   */
  async resetPassword(
    resetPasswordDto: ResetPasswordDto,
    resetPasswordToken: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    try {
      if (!resetPasswordToken) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.MISSING,
          language,
        );
      }

      // Validate passwords match
      if (resetPasswordDto.newPassword !== resetPasswordDto.confirmPassword) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.SYSTEM_ERROR,
          AUTH_MESSAGES.PASSWORD.MISMATCH,
          language,
        );
      }

      // Decode and validate the token
      const decodedToken =
        await this.validateResetPasswordToken(resetPasswordToken);
      const { stateId, cookies } = this.extractTokenData(decodedToken);
      if (!stateId) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.INVALID,
          language,
        );
      }

      // Construct the reset password URL
      const resetPasswordUrl = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;

      // Prepare form data for password reset
      const formData = new URLSearchParams();
      formData.append('operation', 'verify');
      formData.append('password', resetPasswordDto.oldPassword);
      formData.append('newPass', resetPasswordDto.newPassword);
      formData.append('confPass', resetPasswordDto.confirmPassword);
      formData.append('submit', 'login');
      const requestConfig = {
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Encoding': 'gzip, deflate, br, zstd',
          'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookies,
          'Host': 'isam-digital-test.hodomain.local:448',
          'Origin': 'https://isam-digital-test.hodomain.local:448',
          'Pragma': 'no-cache',
          'Referer': resetPasswordUrl,
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'same-origin',
          'Sec-Fetch-User': '?1',
          'Upgrade-Insecure-Requests': '1',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
        },
        maxRedirects: 0,
        httpsAgent: this.agent,
        timeout: 5000,
        validateStatus: (status) => status < 500, // Accept any status code less than 500
      };

      // Send password reset request
      const resetResponse = await lastValueFrom(
        this.httpService.post(
          resetPasswordUrl,
          formData.toString(),
          requestConfig,
        ),
      );
      this.logger.log('Password reset request sent successfully');

      // Process the response
      if (resetResponse.status === 302 || resetResponse.status === 200) {
        this.logger.log('Password reset successful');

        // Extract cookies from reset response
        const resetResponseCookies = resetResponse.headers['set-cookie'] || [];

        // Combine existing cookies with new cookies from reset response
        const combinedCookies = cookies
          ? `${cookies}; ${parseSetCookieHeaders(resetResponseCookies)}`
          : parseSetCookieHeaders(resetResponseCookies);

        // Create a mock user object with the combined cookies for logout
        const mockUser = { cookies: combinedCookies } as CurrentUser;
        // Call logout to terminate the session
        await this.logout(mockUser);
        return buildResponse(
          this.i18nService,
          AuthResponseCode.SUCCESS,
          AUTH_MESSAGES.PASSWORD.RESET_SUCCESS,
          language,
        );
      } else {
        // Check for error messages in the response
        if (
          resetResponse.data.includes(
            'Password does not meet complexity requirements',
          )
        ) {
          return buildResponse(
            this.i18nService,
            AuthResponseCode.SYSTEM_ERROR,
            AUTH_MESSAGES.PASSWORD.COMPLEXITY,
            language,
          );
        }
        return buildResponse(
          this.i18nService,
          AuthResponseCode.SYSTEM_ERROR,
          AUTH_MESSAGES.PASSWORD.RESET_FAILED,
          language,
        );
      }
    } catch (error) {
      this.logger.error('Password reset failed', error);
      if (error instanceof UnauthorizedException) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.INVALID,
          language,
        );
      }
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.PASSWORD.RESET_FAILED,
        language,
      );
    }
  }

  /**
   * Handle terms and conditions acceptance
   */
  async acceptTerms(
    currentUser: CurrentUser,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    try {
      // Create custom payload for terms acceptance
      const customPayload = this.isamService.createRestPortalPayload();
      customPayload.requestData.tandcflag = 'true';
      customPayload.requestData.mode = 'accept_terms';

      // Call ISAM service with custom URL and payload
      const loginUrl = `${this.isamService.baseUrl}/restportal/login/`;

      // Simulate a successful initial response to skip first check
      const response = await this.isamService.performRestPortalAuthentication(
        {
          statusCode: '302', // simulation to skip first check
          status: 302, // simulation to skip first check
          headers: { 'set-cookie': [currentUser.cookies] },
        },
        customPayload,
        loginUrl,
      );

      // Extract data from response
      const responseData = JSON.parse(response.body);

      // Convert user cookies string to array format to combine it later with cookies from request
      const userCookiesArray = currentUser.cookies
        ? currentUser.cookies.split(';').map((cookie) => cookie.trim())
        : [];

      // Get cookies from response
      const responseCookies = this.extractHeadersAndCookies(response);

      // Combine cookies from user and response
      const collectedCookies = [...userCookiesArray, ...responseCookies];

      // Parse the combined cookies
      const parsedCookies = parseSetCookieHeaders(collectedCookies);

      return {
        responseCode: AuthResponseCode.SUCCESS,
        message: this.i18nService.translate(
          AUTH_MESSAGES.TERMS.ACCEPTED,
          language,
        ),
        token: await this.jwtTokenService.generateToken(parsedCookies, null), // no stateId to include it.
        userData: responseData,
      };
    } catch (error) {
      this.logger.error('Terms acceptance failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.TERMS.FAILED,
        language,
      );
    }
  }

  /**
   * Handles the normal login flow, including
   * Failed attempts tracking
   * a Captcha challenge after threshold
   * ISAM authentication and error handling
   */
  private async handleNormalLogin(
    loginDto: LoginDto,
    language: UserLanguage,
  ): Promise<AuthResponseDto> {

    // handle a captcha challenge if it exists, and if it passed move to ISAM part
    const captchaResponse = await this.captchaService.validateCaptchaChallenge(
      loginDto,
      language,
    );
    if (captchaResponse) {
      return captchaResponse;
    }

    // Proceed with ISAM authentication
    try {
      const { stateId, response: portalResponse } =
        await this.stateIdParserService.extractStateId();

      const isamResponse = await this.isamService.performAuthentication(
        loginDto,
        { stateId, response: portalResponse },
      );

      // ISAM: Account locked
      if (this.hasLockedAccountError(isamResponse.body)) {
        this.logger.warn('Authentication failed: Locked Account');
        return buildResponse(
          this.i18nService,
          AuthResponseCode.LOCKED_ACCOUNT,
          AUTH_MESSAGES.LOGIN.LOCKED_ACCOUNT,
          language,
        );
      }

      // ISAM: Incorrect credentials
      if (this.hasIncorrectCredentialsError(isamResponse.body)) {
        this.logger.warn('Authentication failed: Incorrect credentials');

        // Increment failed attempts
        await this.captchaService.incrementFailedAttempts(
          this.captchaService.createHashedLoginAttemptsKey(loginDto.username),
        );

        // Get current attempts count to determine if captcha is needed
        const attemptsKey = this.captchaService.createHashedLoginAttemptsKey(loginDto.username);
        const currentAttempts = await this.captchaService.getFailedAttempts(attemptsKey);

        // If attempts to exceed a threshold, return a captcha challenge
        if (currentAttempts >= this.captchaService.CAPTCHA_THRESHOLD) {
          const { image, token } = await this.captchaService.generateCaptcha();
          return buildCaptchaResponse(
            this.i18nService,
            AuthResponseCode.CAPTCHA_REQUIRED,
            AUTH_MESSAGES.LOGIN.INVALID_CREDENTIALS_CAPTCHA,
            language,
            image,
            token,
          );
        }

        // Otherwise return standard invalid credentials response
        return buildResponse(
          this.i18nService,
          AuthResponseCode.INVALID_CREDENTIALS,
          AUTH_MESSAGES.LOGIN.INVALID_CREDENTIALS,
          language,
        );
      }

      // reset failed attempts, as the coming cases are all valid login.
      await this.captchaService.resetFailedAttempts(
        this.captchaService.createHashedLoginAttemptsKey(loginDto.username),
      );

      // Check if the response contains an error message indicating expired password
      if (this.hasPasswordExpiredError(isamResponse.body)) {
        this.logger.warn('Authentication failed: Password Expired');

        // Generate reset password token
        const resetPasswordToken = await this.extractPasswordResetInfo(
          portalResponse,
          isamResponse,
        );

        return {
          responseCode: AuthResponseCode.EXPIRED_PASSWORD,
          message: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.EXPIRED_PASSWORD,
            language,
          ),
          resetPasswordToken,
        };
      }

      // ISAM: Active session
      if (this.isamService.hasActiveSession(isamResponse.body)) {
        const activeSessionResponse = await this.extractTerminationInfo(
          portalResponse,
          isamResponse,
        );
        return {
          responseCode: AuthResponseCode.ACTIVE_SESSION,
          message: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.ACTIVE_SESSION,
            language,
          ),
          terminationToken: activeSessionResponse.terminationToken,
        };
      }

      // No active session - complete authentication
      const authResult = await this.completeAuthentication(
        portalResponse,
        isamResponse,
        stateId,
      );

      // Check if this is a term acceptance response
      if (authResult.termsRequired) {
        return {
          ...authResult,
          responseCode: AuthResponseCode.TERMS_REQUIRED,
          message: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.TERMS_REQUIRED,
            language,
          ),
          token: authResult.token,
          terms: authResult.terms,
          tokenType: authResult.tokenType,
        };
      }

      // Return a standard authentication result
      return {
        responseCode: AuthResponseCode.SUCCESS,
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.SUCCESS,
          language,
        ),
        token: authResult.token,
        userData: authResult.userData,
      };
    } catch (error) {
      this.logger.error('Login failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.SYSTEM_ERROR,
        language,
      );
    }
  }

  /**
   * Check if the HTML response contains an error message indicating incorrect credentials.
   */
  private hasIncorrectCredentialsError(html: string): boolean {
    return html.includes('Your user or password is incorrect');
  }

  /**
   * Check if the HTML response contains an error message indicating account lock
   */
  private hasLockedAccountError(html: string): boolean {
    return html.includes('Your account has been locked');
  }

  /**
   * Check if the HTML response contains an error message indicating password expired
   */
  private hasPasswordExpiredError(html: string): boolean {
    return html.includes('Your password is expired');
  }

  /**
   * Handle session termination flow
   */
  private async handleTermination(
    terminationHeader: string,
    language: UserLanguage,
  ): Promise<AuthResponseDto> {
    try {
      const result = await this.isamService.terminateSession(terminationHeader);
      return {
        ...result,
        responseCode: AuthResponseCode.SUCCESS,
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.SESSION_TERMINATED,
          language,
        ),
        token: result.token,
        userData: result.userData,
      };
    } catch (error) {
      this.logger.error('Termination failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.TOKEN_ERROR,
        AUTH_MESSAGES.LOGIN.TOKEN_ERROR,
        language,
      );
    }
  }

  /**
   * Extract headers and cookies from response
   */
  private extractHeadersAndCookies(response: any): string[] {
    return response.headers['set-cookie'] || [];
  }

  /**
   * Extract termination information and generate a termination token.
   */
  private async extractTerminationInfo(
    portalResponse: any,
    isamResponse: any,
  ): Promise<ActiveSessionResponse | null> {
    try {
      const terminationStateId = this.stateIdParserService.parseStateIdFromHtml(
        isamResponse.body,
      );
      const portalHeaders = this.extractHeadersAndCookies(portalResponse);
      const isamAuthenticationHeaders =
        this.extractHeadersAndCookies(isamResponse);
      const collectedHeaders = [...portalHeaders, ...isamAuthenticationHeaders];
      const terminationToken = await this.jwtTokenService.generateToken(
        collectedHeaders,
        terminationStateId,
      );
      return {
        terminationToken,
        message: 'Active session detected. Termination required before login.',
      };
    } catch (error) {
      this.logger.error('Failed to extract termination info', error);
      return null;
    }
  }

  /**
   * Complete the authentication process after successful login
   */
  private async completeAuthentication(
    portalResponse: any,
    isamResponse: any,
    stateId: string,
  ): Promise<any> {
    // hit the third call.
    const isamRestPortalResponse =
      await this.isamService.performRestPortalAuthentication(isamResponse);

    const portalHeaders = this.extractHeadersAndCookies(portalResponse);
    const isamAuthenticationHeaders =
      this.extractHeadersAndCookies(isamResponse);
    const isamRestPortalResponseHeaders = this.extractHeadersAndCookies(
      isamRestPortalResponse,
    );
    const collectedHeaders = [
      ...portalHeaders,
      ...isamAuthenticationHeaders,
      ...isamRestPortalResponseHeaders,
    ];
    const parsedHeaders = parseSetCookieHeaders(collectedHeaders);

    // Generate JWT token
    const jwtToken = await this.jwtTokenService.generateToken(
      parsedHeaders,
      stateId,
    );

    // Parse the response body
    const userData = JSON.parse(isamRestPortalResponse.body);

    // Check if this is a terms acceptance response
    if (userData.mode === 'accept_terms' && userData.objectData?.tandctext) {
      this.logger.log('Terms and conditions acceptance required');
      return {
        token: jwtToken,
        termsRequired: true,
        terms: userData.objectData.tandctext,
        tokenType: userData.objectData.tokentype || 'NO_REAUTH',
      };
    }

    // check user details
    // first we need to response indicate he had multiple entities , if so we need to return forbidden response
    // second we need to check the reference of the returned entity if it from the allowed industries.
    const userDetails = await this.userDetailsService.getUserDetails(userData);
    console.log('userDetails', userDetails);

    // Return a standard authentication result
    return {
      token: jwtToken,
      userData: userData,
    };
  }

  /**
   * Extract password reset information and generate token
   */
  private async extractPasswordResetInfo(
    portalResponse: any,
    isamResponse: any,
  ): Promise<string> {
    try {
      const resetStateId = this.stateIdParserService.parseStateIdFromHtml(
        isamResponse.body,
      );
      const portalHeaders = this.extractHeadersAndCookies(portalResponse);
      const isamAuthenticationHeaders =
        this.extractHeadersAndCookies(isamResponse);
      const collectedHeaders = [...portalHeaders, ...isamAuthenticationHeaders];
      return await this.jwtTokenService.generateToken(
        collectedHeaders,
        resetStateId,
      );
    } catch (error) {
      this.logger.error('Failed to extract password reset info', error);
      throw new UnauthorizedException(
        'Failed to generate password reset token',
        IsamErrorCodes.EXPIRED_PASSWORD,
      );
    }
  }

  /**
   * Validate reset password token
   */
  private async validateResetPasswordToken(token: string): Promise<any> {
    try {
      // Extract token from authorization header
      const actualToken = token.replace(/^Bearer\s+/i, '');
      return await this.jwtTokenService.verifyToken(actualToken);
    } catch (error) {
      this.logger.error('Invalid reset password token', error);
      throw new UnauthorizedException(
        'Invalid reset password token',
        'INVALID_TOKEN',
      );
    }
  }

  /**
   * Extract stateId and cookies from a decoded JWT token.
   */
  private extractTokenData(decodedToken: any): {
    stateId: string;
    cookies: string;
  } {
    return {
      stateId: decodedToken.stateId,
      cookies: decodedToken.cookies,
    };
  }
}
