import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { FccRequestsEnum, UserLanguage } from '@enums';
import { ApiAdapterClientService, I18nLookupService } from '@modules';
import { CurrentUser } from '@types';

@Injectable()
export class UserDetailsService {
  private readonly logger: Logger;
  private readonly agent;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nLookupService,
    private readonly apiAdapterService: ApiAdapterClientService,
  ) {
    this.logger = new Logger(UserDetailsService.name);
  }

  /**
   * Fetch user details by ID
   */
  async getUserDetails(user: CurrentUser) {
    try {
      this.logger.log(`Fetching user details`);

      return this.apiAdapterService.fccRequest({
        requestId: FccRequestsEnum.USER_DETAILS,
        user,
      });

    } catch (error) {
      this.logger.error('Failed to fetch user details', error);
      throw error;
    }
  }
}

