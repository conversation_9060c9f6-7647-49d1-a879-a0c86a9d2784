import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { LoginDto } from '../dto/login.dto';
import { lastValueFrom } from 'rxjs';
import { AxiosRequestConfig } from 'axios';
import * as https from 'node:https';
import { JwtTokenService } from '@/auth/services/jwt-token.service';
import { IsamErrorCodes } from '@/auth/types/isam.types';
import { parseSetCookieHeaders } from '@/auth/utils/cookie.utils';
import { JSDOM } from 'jsdom';
import { StateIdParserService } from './stateIdParserService';
import { UserDetailsService } from '@/auth/services/user-details.service';


class IsamError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number,
    public details?: any,
  ) {
    super(message);
    this.name = 'IsamError';
  }
}

@Injectable()
export class IsamService {
  readonly baseUrl: string;
  readonly loginBaseUrl: string;
  private readonly logger = new Logger(IsamService.name);
  private readonly agent: https.Agent;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private jwtTokenService: JwtTokenService,
    private userDetailsService: UserDetailsService,
  ) {
    this.baseUrl = this.configService.getOrThrow('ISAM_BASE_URL');
    this.loginBaseUrl = this.configService.getOrThrow('ISAM_BASE_URL').replace(/\/FBCCBB$/, '');
    this.agent = new https.Agent({ rejectUnauthorized: false });
  }

  /**
   * Perform authentication request
   */
  async performAuthentication(
    loginDto: LoginDto,
    data: any,
  ): Promise<{
    statusCode: string
    headers: Record<string, string | string[]>
    body: string
  }> {
    try {
      const loginUrl = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${data.stateId}`;
      const cookies = data.response.headers['set-cookie'].join('');
      const headers = this.getAuthenticationHeaders(cookies);
      const requestConfig = this.getRequestConfig(headers);
      const formData = this.createAuthFormData(loginDto);

      const response = await lastValueFrom(
        this.httpService.post(loginUrl, formData.toString(), requestConfig),
      );

      return this.formatResponse(response);
    } catch (error) {
      this.logger.error('Authentication request failed', error);
      throw new IsamError('Failed to perform authentication request', IsamErrorCodes.INVALID_CREDENTIALS, 401);
    }
  }

  /**
   * Perform REST portal authentication with custom payload
   */
  public async performRestPortalAuthentication(isamResponse: any, customPayload?: any, customUrl?: string) {
    if (isamResponse.statusCode != 302 && isamResponse.status != 302) {
      throw new IsamError('Failed to perform authentication request', IsamErrorCodes.INVALID_CREDENTIALS, 401);
    }

    try {
      const loginUrl = customUrl || `${this.baseUrl}/restportal/login/`;
      const cookies = isamResponse.headers['set-cookie']?.join(';') || '';
      const headers = { 'Cookie': cookies, 'Connection': 'keep-alive' };
      const requestConfig = this.getRequestConfig(headers);
      const payload = customPayload || this.createRestPortalPayload();

      const response = await lastValueFrom(
        this.httpService.post(loginUrl, payload, requestConfig),
      );

      return this.formatResponse(response);
    } catch (error) {
      this.logger.error('REST Portal authentication failed', error);
      throw new IsamError('Failed to perform authentication request', IsamErrorCodes.INVALID_CREDENTIALS, 401);
    }
  }

  /**
   * Check if response indicates an active session
   */
  public hasActiveSession(html: string): boolean {
    const dom = new JSDOM(html);
    const heading = dom.window.document.querySelector('.form-signin-heading')?.textContent?.toLowerCase();

    return heading?.includes('you are already has an active session') ?? false;
  }


  /**
   * Terminate an active session
   */
  public async terminateSession(terminationToken: string): Promise<any> {
    try {
      const decodedToken = await this.validateTerminationToken(terminationToken);
      const { stateId, cookies } = this.extractTokenData(decodedToken);

      if (!stateId) {
        throw new IsamError('Missing state ID in token', 'INVALID_TOKEN', 401);
      }

      const terminateUrl = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;
      const formData = new URLSearchParams();
      formData.append('operation', 'verify');
      formData.append('sub', 'true');

      const requestConfig = {
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Encoding': 'gzip, deflate, br, zstd',
          'Accept-Language': 'en-GB,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookies,
          'Host': this.loginBaseUrl,
          'Origin': this.loginBaseUrl,
          'Pragma': 'no-cache',
          'Referer': terminateUrl,
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'same-origin',
          'Sec-Fetch-User': '?1',
          'Upgrade-Insecure-Requests': '1',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
        }
        ,
        maxRedirects: 0,
        validateStatus: (status) => status < 500,
      };

      const terminationPortalResponse = await lastValueFrom(
        this.httpService.post(terminateUrl, formData.toString(), requestConfig),
      );
      return this.processTerminationResponse(terminationPortalResponse, cookies, stateId);
      this.logger.log('Active session terminated successfully');
    } catch (error) {
      return this.handleTerminationError(error);
    }
  }

  /**
   * Create REST portal payload
   */
  public createRestPortalPayload(credentials?: { username?: string, company?: string, password?: string }) {
    return {
      'userData': {
        'username': credentials?.username || 'username',
        'company': credentials?.company || 'company',
        'userSelectedLanguage': 'en',
      },
      'requestData': {
        'password': credentials?.password || '******',
        'mode': '',
        'tandcflag': '',
        'newUserName': '',
        'newPasswordValue': '',
        'newPasswordConfirm': '',
        'phone': '',
        'email': '',
        'nextScreen': '/portal/screen/GTPLoginScreen',
        'preferredMode': '',
      },
      'vascoData': {
        'vasco_primary_key_web': '',
        'vasco_serial_number_web': '',
        'vasco_mobile_number_web': '',
        'vasco_token_web': '',
        'vasco_account_name_web': '',
        'vasco_finger_print_web': '',
        'vasco_OTP_web': '',
      },
    };
  }

  /**
   * Initiate forget password process
   * @param username User's username
   * @param company User's company
   * @param stateIdParserService StateIdParserService instance
   * @returns HTML response containing reset options
   */
  async initiateForgetPassword(
    username: string,
    company: string,
    stateIdParserService: StateIdParserService,
  ): Promise<{
    statusCode: string;
    headers: Record<string, string | string[]>;
    body: string;
  }> {
    try {
      const forgetPasswordUrl = `${this.baseUrl}/mga/sps/authsvc?PolicyId=urn:ibm:security:authentication:asf:testForget`;

      // Create request configuration
      const requestConfig = {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'text/html,application/xhtml+xml,application/xml',
        },
        httpsAgent: this.agent,
        maxRedirects: 0,
        validateStatus: (status) => status < 500,
      };

      // Make the initial request to get the forget password page
      const response = await lastValueFrom(
        this.httpService.get(forgetPasswordUrl, requestConfig),
      );

      // Extract stateId from the response using StateIdParserService
      const stateId = stateIdParserService.parseStateIdFromHtml(response.data);

      if (!stateId) {
        throw new Error('Failed to extract stateId from forget password response');
      }

      // Prepare form data for forget password request
      const formData = new URLSearchParams();
      formData.append('username', username);
      formData.append('company', company);
      formData.append('operation', 'verify');
      formData.append('Submit', 'Login');

      // Submit the forget password form
      const submitUrl = `${this.baseUrl}/mga/sps/authsvc?StateId=${stateId}`;
      const submitResponse = await lastValueFrom(
        this.httpService.post(submitUrl, formData.toString(), {
          ...requestConfig,
          headers: {
            ...requestConfig.headers,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }),
      );

      this.logger.log('Forget password request initiated successfully');

      return this.formatResponse(submitResponse);
    } catch (error) {
      this.logger.error('Forget password request failed', error);
      throw new IsamError(
        'Failed to initiate forget password request',
        IsamErrorCodes.INVALID_CREDENTIALS,
        401,
        error,
      );
    }
  }

  /**
   * Process the termination response and generate a new token
   */
  private async processTerminationResponse(response, cookies, stateId) {
    const terminationPortalResponseHeaders = response.headers['set-cookie'];
    const isamRestPortalResponse = await this.performRestPortalAuthentication(response);
    const isamAuthenticationHeaders = isamRestPortalResponse.headers['set-cookie'];

    const collectedHeaders = [
      ...cookies,
      ...terminationPortalResponseHeaders,
      ...isamAuthenticationHeaders,
    ];
    const parsedHeaders = parseSetCookieHeaders(collectedHeaders);

    // Generate JWT token
    const jwtToken = await this.jwtTokenService.generateToken(parsedHeaders, stateId);

    // Parse the response body
    const userData = JSON.parse(isamRestPortalResponse.body);

    // Check if this is a terms acceptance response
    if (userData.mode === 'accept_terms' && userData.objectData?.tandctext) {
      this.logger.log('Terms and conditions acceptance required');
      return {
        token: jwtToken,
        termsRequired: true,
        terms: userData.objectData.tandctext,
        tokenType: userData.objectData.tokentype || 'NO_REAUTH',
      };
    }

    // check user details
    // first we need to response indicate he had multiple entities , if so we need to return forbidden response
    // second we need to check the reference of the returned entity if it from the allowed industries.
    const userDetails = await this.userDetailsService.getUserDetails(userData);
    console.log('userDetails', userDetails);


    // Return standard authentication result
    return {
      token: jwtToken,
      userData: userData,
    };
  }

  /**
   * Validate the termination token
   */
  private async validateTerminationToken(token: string) {
    try {
      const decodedToken = await this.jwtTokenService.decodeAndValidateToken(token);
      if (!decodedToken) {
        throw new Error('Token decoded but returned empty result');
      }
      return decodedToken;
    } catch (error) {
      this.logger.error(`Token validation failed: ${error.message}`, {
        errorName: error.name,
        errorType: error.constructor.name,
        errorCode: error.status || error.code,
        stack: error.stack,
      });

      if (error instanceof UnauthorizedException) {
        const errorCode = error.message.includes('expired') ? 'TOKEN_EXPIRED' :
          error.message.includes('revoked') ? 'TOKEN_REVOKED' : 'INVALID_TOKEN';

        throw new IsamError(
          `Invalid termination token: ${error.message}`,
          errorCode,
          401,
          { originalError: error },
        );
      }

      throw new IsamError(
        `Invalid termination token: ${error.message || 'Unknown error'}`,
        'INVALID_TOKEN',
        401,
        error,
      );
    }
  }

  /**
   * Handle termination errors
   */
  private handleTerminationError(error) {
    if (error instanceof IsamError) {
      throw error;
    }
    this.logger.error('Session termination failed', error);
    throw new IsamError('Failed to terminate active session', IsamErrorCodes.SESSION_TERMINATION_FAILED, 400, error);
  }

  /**
   * Extract stateId and headers from decoded JWT token
   */
  private extractTokenData(decodedToken: any): { stateId: string, cookies: string } {
    try {
      let stateId: string;
      let cookies: string;

      if (typeof decodedToken === 'string') {
        stateId = decodedToken;
      } else if (typeof decodedToken === 'object') {
        stateId = decodedToken.stateId || decodedToken.terminationStateId;
        cookies = decodedToken.cookies || '';
      } else {
        throw new Error('Unexpected token format');
      }

      return { stateId, cookies };
    } catch (error) {
      this.logger.error('Failed to extract data from token', error);
      throw new IsamError('Invalid token structure', 'INVALID_TOKEN', 401, error);
    }
  }

  /**
   * Create authentication form data
   */
  private createAuthFormData(loginDto: LoginDto): URLSearchParams {
    const formData = new URLSearchParams();
    formData.append('username', loginDto.username);
    formData.append('password', loginDto.password);
    formData.append('company', loginDto.company);
    formData.append('operation', 'verify');
    formData.append('g-recaptcha-response', '');
    formData.append('Submit', '');
    return formData;
  }

  /**
   * Get authentication headers
   */
  private getAuthenticationHeaders(cookies: string) {

    return {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
      'Accept-Encoding': 'gzip, deflate, br, zstd',
      'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Content-Type': 'application/x-www-form-urlencoded',
      'Cookie': cookies,
      'Host': 'isam-digital-test.hodomain.local:448',
      'Origin': 'https://isam-digital-test.hodomain.local:448',
      'Pragma': 'no-cache',
      'Referer': 'https://isam-digital-test.hodomain.local:448/FBCCBB/portal',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'same-origin',
      'Sec-Fetch-User': '?1',
      'Upgrade-Insecure-Requests': '1',
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
    };
  }

  /**
   * Get request configuration
   */
  private getRequestConfig(headers: any): AxiosRequestConfig {
    return {
      headers,
      httpsAgent: this.agent,
      timeout: 10000,
      maxRedirects: 0,
      validateStatus: null,
    };
  }

  /**
   * Format response
   */
  private formatResponse(response: any) {
    return {
      statusCode: String(response.status),
      headers: response.headers as Record<string, string | string[]>,
      body: typeof response.data === 'string' ? response.data : JSON.stringify(response.data),
    };
  }
}
