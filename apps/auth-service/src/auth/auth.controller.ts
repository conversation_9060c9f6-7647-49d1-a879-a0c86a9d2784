import { Body, Controller, Get, Headers, HttpCode, HttpStatus, Post, UseGuards } from '@nestjs/common';
import { AuthService } from './services/auth.service';
import { LoginDto } from './dto/login.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { AuthResponseDto } from './dto/auth-response.dto';
import { getCurrentUser, I18nLookupService, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { AuthResponseCode } from './types/auth-response.types';
import { AUTH_MESSAGES } from './constants/auth-messages.constants';
import { parseLanguageHeader } from './utils/language.utils';
import { CaptchaService } from './services/captcha.service';
import { ForgetPasswordService } from './services/forget-password.service';
import { ForgetPasswordOptionsDto } from './dto/forget-password-options.dto';

@Controller()
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly i18nService: I18nLookupService,
    private readonly captchaService: CaptchaService,
    private readonly forgetPasswordService: ForgetPasswordService,
  ) {
  }

  /**
   * User login endpoint.
   * Captcha fields are handled via LoginDto and returned in response if required.
   */
  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(
    @Body() loginDto: LoginDto,
    @Headers('authorization') terminationHeader?: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    // Default to English if no language is provided or if the language is not supported
    const userLanguage = parseLanguageHeader(language);
    return this.authService.login(loginDto, terminationHeader, userLanguage);
  }

  @Post('dev/login')
  @HttpCode(HttpStatus.OK)
  async devLogin(@Body() loginDto: LoginDto): Promise<AuthResponseDto> {
    return this.authService.devLogin(loginDto);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async logout(
    @getCurrentUser() currentUser: CurrentUser,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    return this.authService.logout(currentUser);
  }

  @Get('refresh-token')
  @HttpCode(HttpStatus.OK)
  async refreshToken(
    @Headers('authorization') authorization: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    try {
      const result = await this.authService.refreshToken(authorization);
      return {
        responseCode: AuthResponseCode.SUCCESS,
        message: this.i18nService.translate(
          AUTH_MESSAGES.TOKEN.REFRESH_SUCCESS,
          userLanguage,
        ),
        token: result.token,
        nearExpiry: result.nearExpiry,
      };
    } catch (error) {
      return {
        responseCode: AuthResponseCode.TOKEN_ERROR,
        message: this.i18nService.translate(
          AUTH_MESSAGES.TOKEN.REFRESH_FAILED,
          userLanguage,
        ),
      };
    }
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
    @Headers('authorization') resetPasswordToken: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    return this.authService.resetPassword(
      resetPasswordDto,
      resetPasswordToken,
      userLanguage,
    );
  }

  @Post('accept-terms')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async acceptTerms(
    @getCurrentUser() currentUser: CurrentUser,
    @Headers('authorization') token: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    return this.authService.acceptTerms(currentUser, userLanguage);
  }

  /**
   * Get a new captcha image and token.
   * Used to refresh the captcha.
   */
  @Get('captcha')
  async getCaptcha() {
    const { image, token } = await this.captchaService.generateCaptcha();
    return { image, token };
  }

  /**
   * Get available forget password options for a user
   */
  @Post('forget-password/options')
  @HttpCode(HttpStatus.OK)
  async getForgetPasswordOptions(
    @Body() forgetPasswordOptionsDto: ForgetPasswordOptionsDto,
    @Headers('accept-language') language?: string,
  ) {
    const userLanguage = parseLanguageHeader(language);
    return this.forgetPasswordService.getForgetPasswordOptions(
      forgetPasswordOptionsDto,
      userLanguage,
    );
  }
}
