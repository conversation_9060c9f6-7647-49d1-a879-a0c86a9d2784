import { Injectable } from '@nestjs/common';
import { CurrentUser, IndividualAccountBalance } from '@types';
import { AccountsSummaryService } from './services/accounts.service';
import { AccountType, CurrencyEnum } from '@enums';
import { MutualFundsSummaryService } from './services/mutual-funds.service';
import { FxService } from '@modules';
import { LoansSummaryService } from './services/loans.service';

@Injectable()
export class SummaryService {
  constructor(
    private readonly accountsService: AccountsSummaryService,
    private readonly mutualFundsService: MutualFundsSummaryService,
    private readonly loansService: LoansSummaryService,
    private readonly fxService: FxService,
  ) {}

  async getSummary(currentUser: CurrentUser) {
    const { totalAccountBalanceOnBaseCurrency, individualAccountBalances } =
      await this.accountsService.getSummary(currentUser);

    const { currencyCode } = totalAccountBalanceOnBaseCurrency;

    const mutualFundsTotalInEGP =
      await this.mutualFundsService.getTotal(currentUser);

    const totalMutualFundsBalanceOnBaseCurrency = {
      amount: mutualFundsTotalInEGP,
      currencyCode,
    };

    const loansTotal = await this.loansService.getTotal(currentUser);

    const totalLoansBalanceOnBaseCurrency = {
      amount: loansTotal,
      currencyCode,
    };

    const depositsBalanceOnBaseCurrency = {
      amount: this.calculateTotalDeposits(individualAccountBalances),
      currencyCode,
    };

    if (currencyCode != CurrencyEnum.EGP) {
      totalMutualFundsBalanceOnBaseCurrency.amount =
        await this.fxService.convert(
          CurrencyEnum.EGP,
          CurrencyEnum[currencyCode],
          mutualFundsTotalInEGP,
          currentUser,
        );
    }

    totalAccountBalanceOnBaseCurrency.amount +=
      totalMutualFundsBalanceOnBaseCurrency.amount;

    return {
      totalAccountBalanceOnBaseCurrency,
      individualAccountBalances: this.filterUserAccounts(
        individualAccountBalances,
      ),
      totalLoansBalanceOnBaseCurrency,
      totalMutualFundsBalanceOnBaseCurrency,
      depositsBalanceOnBaseCurrency,
    };
  }

  filterUserAccounts(accounts: IndividualAccountBalance[]) {
    return accounts?.filter(
      (account) =>
        account.accountType === AccountType.CURRENT ||
        account.accountType === AccountType.SAVINGS,
    );
  }

  calculateTotalDeposits(accounts: IndividualAccountBalance[]) {
    return accounts
      ?.filter((account) => account.accountType === AccountType.TERMDEPOSIT)
      ?.reduce((sum, item) => {
        return sum + item.availableBalanceConverted;
      }, 0);
  }
}
