import { Modu<PERSON> } from '@nestjs/common';
import { SummaryController } from './summary.controller';
import { SummaryService } from './summary.service';
import { MutualFundsSummaryService } from './services/mutual-funds.service';
import { AccountsSummaryService } from './services/accounts.service';
import { LoansSummaryService } from './services/loans.service';

@Module({
  controllers: [SummaryController],
  providers: [
    SummaryService,
    MutualFundsSummaryService,
    AccountsSummaryService,
    LoansSummaryService,
  ],
})
export class SummaryModule {}
