import { AccountType, FccRequestsEnum } from '@enums';
import { ApiAdapterClientService } from '@modules';
import { Injectable } from '@nestjs/common';
import { CurrentUser } from '@types';

@Injectable()
export class LoansSummaryService {
  constructor(
    private readonly apiAdapterClientService: ApiAdapterClientService,
  ) {}

  async getTotal(currentUser: CurrentUser) {
    const loans = await this.apiAdapterClientService.fccRequest<
      Record<string, number>[]
    >({
      requestId: FccRequestsEnum.LOANS_SUMMARY,
      user: currentUser,
    });

    let total = 0;

    if (loans.length > 0) {
      total = loans.reduce((sum, item) => {
        return sum + item.convertedPrincipalAmount;
      }, 0);
    }

    return total;
  }
}
