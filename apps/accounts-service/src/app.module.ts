// app.module.ts
import { Module } from '@nestjs/common';
import {
  ApiAdapterClient,
  GlobalConfigModule,
  RedisModule,
  JwtAuthModule,
  FxModule,
} from '@modules';
import defaults from './config/defaults';
import { SummaryModule } from './modules/summary/summary.module';
import { AccountsModule } from './modules/accounts/accounts.module';

@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    RedisModule.forRootAsync(),
    ApiAdapterClient,
    JwtAuthModule.forRoot(),
    FxModule,
    AccountsModule,
    SummaryModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
