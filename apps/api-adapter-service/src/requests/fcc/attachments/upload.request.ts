import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccMultiPartRequest } from '@/adapter/requests/fcc-multipart.request';
import * as FormData from 'form-data';

@Injectable()
@Request(FccRequestsEnum.UPLOAD_ATTACHMENT, RequestModuleEnum.FCC, 'v1')
export class UploadAttachmentRequest extends FccMultiPartRequest {
  requestMethod: RequestMethod = RequestMethod.POST;

  ApiUrl(): string {
    return `upload`;
  }

  getRequestPayload(payload?: any): any {
    const formData = new FormData();

    formData.append('file', Buffer.from(payload.file.base64, 'base64'), {
      filename: payload.file.filename,
      contentType: payload.file.mimetype,
    });

    formData.append('fileName', payload.fileName);
    formData.append('fileTitle', payload.fileTitle);
    formData.append('identifier', payload.identifier);
    formData.append('refId', payload.refId);
    formData.append('tnxId', payload.tnxId);
    formData.append('entity', payload.entity || '');

    return formData;
  }
}
