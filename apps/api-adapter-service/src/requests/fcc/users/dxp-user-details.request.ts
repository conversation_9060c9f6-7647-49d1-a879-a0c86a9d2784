import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';

@Injectable()
@Request(FccRequestsEnum.DXP_USER_DETAILS, RequestModuleEnum.FCC)
export class DxpUserDetailsRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;

  ApiUrl(): string {
    return 'dxp-user-details';
  }
}
