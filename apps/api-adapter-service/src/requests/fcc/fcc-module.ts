import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AccountBalanceRequest } from './accounts/balance.request';
import { MyPendingTransactionsRequest } from './transactions/my-pending.request';
import { RejectTransactionRequest } from './transactions/reject.request';
import { TransactionDetailsRequest } from './transactions/details.request';
import { AccountsListRequest } from '@/requests/fcc/accounts/list.request';
import { FxRateRequest } from '@/requests/fcc/global/fx-rate.request';
import { InternalTransferInitiateRequest } from '@/requests/fcc/transfers/internal-transfers/initiate.request';
import { UploadAttachmentRequest } from '@/requests/fcc/attachments/upload.request';
import { AuthTypeRequest } from '@/requests/fcc/global/auth-type.request';
import { ApproveTransactionRequest } from './transactions/approve.request';
import { UserPermissionsRequest } from './users/user-permission.request';
import { BeneficiariesListRequest } from './transfers/beneficiaries/list.request';
import { CountryListRequest } from './static-data/country-list.request';
import { CurrencyListRequest } from './static-data/currency-list.request';
import { MutualFundSummaryRequest } from './mutual-fund/summary.request';
import { LoanAccountSummaryRequest } from './loans/summary.request';
import { AccountDetailsRequest } from './accounts/account-details';
import { BeneficiaryDeleteRequest } from './transfers/beneficiaries/delete.request';
import { TPTBeneficiaryCreateRequest } from './transfers/beneficiaries/tpt-create.request';
import { TPTBeneficiaryUpdateRequest } from './transfers/beneficiaries/tpt-update.request';
import { MT103BeneficiaryCreateRequest } from './transfers/beneficiaries/mt103-create.request';
import { MT103BeneficiaryUpdateRequest } from './transfers/beneficiaries/mt103-update.request';
import { SwiftDetailsRequest } from './banks/swift-details.request';
import { UserDetailsRequest } from './users/user-details.request';
import { InternalTransferSubmitRequest } from './transfers/internal-transfers/submit.request';
import { UserReauthenticationTypeRequest } from './users/reauthentication-type.request';
import { ClearingSystemRequestRequest } from './banks/clearing-system.request';
import { LocalTransferInitiateRequest } from './transfers/local-transfers/initiate.request';
import { LocalTransferSubmitRequest } from './transfers/local-transfers/submit.request';
import { SwiftTransferInitiateRequest } from './transfers/swift-transfers/initiate.request';
import { SwiftTransferSubmitRequest } from './transfers/swift-transfers/submit.request';
import { AccountSummaryRequest } from './accounts/home-summary.request';
import { TransactionJourneyRequest } from './transactions/journey.request';
import { DeleteAttachmentRequest } from './attachments/delete.request';
import { ListAttachmentRequest } from './attachments/list.request';
import { BanksListRequest } from './static-data/banks-list.request';
import { TransactionHistoryRequest } from './transactions/journey.history';
import { ConfigurationDetailsRequest } from './global/config-details.request';
import { AccountsBalanceSummaryRequest } from './accounts/balance-summary.request';
import { FxConvertRequest } from './global/fx-convert.request';
import { SecureMailInitiateRequest } from '@/requests/fcc/secure-mail/initiate.request';
import { SecureMailSubmitRequest } from '@/requests/fcc/secure-mail/submit.request';

@Module({
  imports: [],
  controllers: [],
  providers: [
    AccountsListRequest,
    AccountBalanceRequest,
    MyPendingTransactionsRequest,
    ApproveTransactionRequest,
    RejectTransactionRequest,
    TransactionDetailsRequest,
    AccountsListRequest,
    FxRateRequest,
    InternalTransferInitiateRequest,
    UploadAttachmentRequest,
    AuthTypeRequest,
    AccountSummaryRequest,
    UserPermissionsRequest,
    UserDetailsRequest,
    CountryListRequest,
    CurrencyListRequest,
    BeneficiariesListRequest,
    MutualFundSummaryRequest,
    BeneficiariesListRequest,
    BeneficiaryDeleteRequest,
    TPTBeneficiaryCreateRequest,
    TPTBeneficiaryUpdateRequest,
    MT103BeneficiaryCreateRequest,
    MT103BeneficiaryUpdateRequest,
    SwiftDetailsRequest,
    InternalTransferSubmitRequest,
    LocalTransferInitiateRequest,
    LocalTransferSubmitRequest,
    UserReauthenticationTypeRequest,
    LoanAccountSummaryRequest,
    AccountDetailsRequest,
    ClearingSystemRequestRequest,
    SwiftTransferInitiateRequest,
    SwiftTransferSubmitRequest,
    TransactionJourneyRequest,
    DeleteAttachmentRequest,
    ListAttachmentRequest,
    BanksListRequest,
    TransactionHistoryRequest,
    ConfigurationDetailsRequest,
    AccountsBalanceSummaryRequest,
    FxConvertRequest,
    SecureMailInitiateRequest,
    SecureMailSubmitRequest,
  ],
})
export class FCCModule {}
