import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import * as snakecaseKeys from 'snakecase-keys';
import { DateTime } from 'luxon';
import { FccFtGenericTnxResponseDto } from '@dtos';

@Injectable()
@Request(FccRequestsEnum.SWIFT_TRANSFER_SUBMIT, RequestModuleEnum.FCC, 'v1')
export class SwiftTransferSubmitRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto = FccFtGenericTnxResponseDto;

  ApiUrl(): string {
    return `genericsave`;
  }

  getRequestPayload(payload: any) {
    const {
      attachments = [],
      refId,
      tnxId,
      paymentDetailsFt = '',
      transferDate,
      ...restPayload
    } = payload;
    const snakeCasePayload = snakecaseKeys(restPayload);

    const todayDate = DateTime.now().toFormat('dd/MM/yyyy');

    return {
      common: {
        screen: 'FundTransferScreen',
        operation: 'SUBMIT',
        option: 'REMITTANCE_CORP',
        mode: 'DRAFT',
        referenceid: refId,
        tnxid: tnxId,
        tnxtype: '01',
      },
      transaction: {
        product_code: 'FT',
        sub_product_code: 'MT103',
        ft_type: '09',
        option_for_tnx: 'MT103',
        sub_product_code_unsigned: 'MT103',
        product_type: 'MT103',

        swiftBicCodeRegexValue:
          '^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9][A-Z2-9]([A-Z0-9]{3,3}){0,1}$',
        swiftregexValue: "^[a-zA-Z0-9+:,/'?.+()\r\n-]*$",
        regexValue: '^[A-Za-z][A-Za-z0-9+_]*$',
        allowedProducts: 'TRSRY,DD,PICO,PIDD,INT,TPT',

        is798: '',
        old_ctl_dttm: '',
        old_inp_dttm: '',
        org_term_year: '',
        org_inco_term: '',
        bulk_template_id: '',
        bulk_ref_id: '',
        bulk_tnx_id: '',

        issuing_bank_name: 'CIB',
        issuing_bank_abbv_name: 'CIB',
        issuing_bank_iso_code: '',

        counterparty_id: '',
        beneficiary_mode: '',

        modifiedBeneficiary: '',

        pre_approved: '692',
        pre_approved_status: 'Y',

        fx_tolerance_rate_value: '',
        applicant_act_nickname: '',
        beneficiary_nickname: '',
        beneficiary_act_nickname: '',
        applicant_act_name1: '',
        beneficiary_account_name1: '',
        fx_tolerance_rate_amt_value: '',
        intermediary_flag: '',
        reauth_perform: 'Y',

        applicant_act_pab: 'Y',

        AccountBalance: '0',

        recurring_flag: 'N',
        recurring_payment_enabled: 'N',

        appl_date: todayDate,

        template_id: '',
        recurring_start_date: '',
        recurring_frequency: '',
        recurring_on: '',
        recurring_end_date: '',
        appl_date_hidden: todayDate,
        allow_both_fields: 'N',

        beneficiary_clearing_system_id: '',
        clearing_system: 'SWIFT',

        sy_iban_no_mt202: '',
        branch_address_flag: 'Y',
        intermediary_bank_swift_bic_code: '',
        clearing_system_id: '',
        intermediary_bank_name: '',
        intermediary_bank_address_line_1: '',
        intermediary_bank_address_line_2: '',
        intermediary_bank_dom: '',
        intermediary_bank_country: '',
        account_with_beneficiary_bank: '',

        cust_ref_id: '',
        payment_details_to_beneficiary: paymentDetailsFt,
        iss_date: transferDate || todayDate,
        iss_date_unsigned: '',
        debit_account_for_charges: '',
        charge_act_cur_code: '',
        charge_act_no: '',
        base_cur_code: 'EGP',
        request_date_unsigned: '',
        display_entity_unsigned: '',
        ft_cur_code_unsigned: '',
        ft_amt_unsigned: '',
        recurring_start_date_unsigned: '',
        recurring_end_date_unsigned: '',
        payment_fee_details: '',
        has_intermediary: 'false',
        fx_rates_type_temp: '',
        fx_master_currency: '',
        pre_approved_beneficiary_status: 'Y',

        fx_rates_type: '01',
        fx_exchange_rate: '',
        fx_exchange_rate_cur_code: '',
        fx_exchange_rate_amt: '',
        fx_tolerance_rate: '',
        fx_tolerance_rate_cur_code: '',
        fx_tolerance_rate_amt: '',

        fxBuyOrSell: '',
        fxTnxAmt: '',
        fx_rate_custom: '',
        fx_dealer_name: '',
        fx_nbr_contracts: '',
        instruction_to_bank: '',

        bene_adv_beneficiary_id_no_send: '',
        bene_adv_mailing_name_add_1_no_send: '',
        bene_adv_mailing_name_add_2_no_send: '',
        bene_adv_mailing_name_add_3_no_send: '',
        bene_adv_mailing_name_add_4_no_send: '',
        bene_adv_mailing_name_add_5_no_send: '',
        bene_adv_mailing_name_add_6_no_send: '',
        bene_adv_postal_code_no_send: '',
        bene_adv_country_no_send: '',

        bene_adv_email_no_send: '',
        bene_adv_email_no_send1: '',

        beneficiary_postal_code: '',
        beneficiary_country: '',
        bene_adv_fax_no_send: '',
        bene_adv_ivr_no_send: '',
        bene_adv_phone_no_send: '',
        notify_beneficiary: 'N',
        notify_beneficiary_choice: '',
        notify_beneficiary_email: '',
        free_format_text: '',

        tnx_amt: payload.ftAmt,
        tnx_cur_code: payload.ftCurCode,

        ...snakeCasePayload,

        attachments: {
          docId: attachments,
        },
      },
    };
  }
}
