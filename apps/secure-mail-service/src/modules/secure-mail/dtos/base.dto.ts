import { SecureMailTypesEnum } from '@enums';
import { <PERSON><PERSON><PERSON><PERSON>, IsEnum, IsOptional, IsString } from 'class-validator';

export class SecureMailBaseDto {
  @IsString()
  @IsEnum(SecureMailTypesEnum)
  type: SecureMailTypesEnum;

  @IsOptional()
  @IsString()
  message: string;

  @IsString()
  @IsOptional()
  reauthPassword: string;

  @IsOptional()
  @IsArray()
  attachments: string[];
}

export class SecureMailInitiateDto {
  @IsString()
  @IsOptional()
  @IsEnum(SecureMailTypesEnum)
  type?: SecureMailTypesEnum;
}
