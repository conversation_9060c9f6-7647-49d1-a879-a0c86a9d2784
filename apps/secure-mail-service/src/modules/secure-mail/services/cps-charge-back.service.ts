import { Injectable } from '@nestjs/common';
import { CpsChargeBackDto } from '../dtos/cps-charge-back.dto';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';

@Injectable()
export class CpsChargeBackService extends SecureMailBaseService<CpsChargeBackDto> {
  requestBodyDto: ClassConstructor<CpsChargeBackDto> = CpsChargeBackDto;
  fileSystemName: string = 'COR2';

  getText(body: CpsChargeBackDto): string[] {
    return [
      `Fund Transfer number: ${body.fundTransferNumber}`,
      `Amount: ${body.amount}`,
      `Refund to Account Number: ${body.accountNumber}`,
    ];
  }
}
