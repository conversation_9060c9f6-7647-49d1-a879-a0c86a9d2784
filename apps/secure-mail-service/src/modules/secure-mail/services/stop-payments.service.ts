import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { StopPaymentsDto } from '../dtos/stop-payments.dto';

@Injectable()
export class StopPaymentsService extends SecureMailBaseService<StopPaymentsDto> {
  fileSystemName: string = 'SP';
  requestBodyDto: ClassConstructor<StopPaymentsDto> = StopPaymentsDto;

  getText(body: StopPaymentsDto): string[] {
    return [`Fund Transfer Number: ${body.fundTransferNumber}`];
  }
}
