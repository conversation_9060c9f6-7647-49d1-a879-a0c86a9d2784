import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { CancelBankDraftDto } from '../dtos/cancel-bank-draft.dto';

@Injectable()
export class CancelBankDraftService extends SecureMailBaseService<CancelBankDraftDto> {
  fileSystemName: string = 'CP';
  requestBodyDto: ClassConstructor<CancelBankDraftDto> = CancelBankDraftDto;

  getText(body: CancelBankDraftDto): string[] {
    return [
      `Fund Transfer Number: ${body.fundTransferNumber}`,
      `Delivery Branch City: ${body.deliveryBranchCity}`,
      `Delivery Branch Name: ${body.deliveryBranchName}`,
    ];
  }
}
