import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { ChangeUserPrivilegeDto } from '../dtos/change-user-privilege.dto';

@Injectable()
export class ChangeUserPrivilegeService extends SecureMailBaseService<ChangeUserPrivilegeDto> {
  fileSystemName: string = 'COR7';
  requestBodyDto: ClassConstructor<ChangeUserPrivilegeDto> =
    ChangeUserPrivilegeDto;

  getText(body: ChangeUserPrivilegeDto): string[] {
    return [`Privilege: ${body.privilege}`, `User: ${body.user}`];
  }
}
