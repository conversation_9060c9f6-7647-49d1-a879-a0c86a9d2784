import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { CpsAddAmendDto } from '../dtos/cps-add-amend.dto';

@Injectable()
export class CpsAddAmendService extends SecureMailBaseService<CpsAddAmendDto> {
  fileSystemName: string = 'COR3';
  requestBodyDto: ClassConstructor<CpsAddAmendDto> = CpsAddAmendDto;

  getText(body: CpsAddAmendDto): string[] {
    return [
      `Social Insurance Number: ${body.socialInsuranceNumber}`,
      `Tax Registration Number: ${body.TaxRegistrationNumber}`,
    ];
  }
}
