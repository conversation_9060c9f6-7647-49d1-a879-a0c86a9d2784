import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { AddNewPermissionDto } from '../dtos/add-user-permission.dto';

@Injectable()
export class AddNewUserPrivilegeService extends SecureMailBaseService<AddNewPermissionDto> {
  fileSystemName: string = 'ERQ';
  requestBodyDto: ClassConstructor<AddNewPermissionDto> = AddNewPermissionDto;

  getText(body: AddNewPermissionDto): string[] {
    return [
      `Access Permission: ${body.accessPermission}`,
      `User: ${body.user}`,
      `Token Type: ${body.tokenType}`,
    ];
  }
}
