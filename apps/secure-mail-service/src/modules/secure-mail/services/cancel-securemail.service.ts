import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { CancelSecureMailDto } from '../dtos/cancel-sercuremail.dto';

@Injectable()
export class CancelSecureMailService extends SecureMailBaseService<CancelSecureMailDto> {
  fileSystemName: string = 'COR6';
  requestBodyDto: ClassConstructor<CancelSecureMailDto> = CancelSecureMailDto;

  getText(body: CancelSecureMailDto): string[] {
    return [
      `Secure Mail Type: ${body.secureMailType}`,
      `Reference Number: ${body.referenceNumber}`,
    ];
  }
}
