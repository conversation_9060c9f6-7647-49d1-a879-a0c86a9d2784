import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { CpsAmendServiceDto } from '../dtos/cps-amend-service.dto';

@Injectable()
export class CpsAmendServiceService extends SecureMailBaseService<CpsAmendServiceDto> {
  requestBodyDto: ClassConstructor<CpsAmendServiceDto> = CpsAmendServiceDto;
  fileSystemName: string = 'COR3';

  getText(body: CpsAmendServiceDto): string[] {
    return [
      `Social Insurance Number: ${body.socialInsuranceNumber}`,
      `Tax Registration Number: ${body.taxRegisterationNumber}`,
    ];
  }
}
