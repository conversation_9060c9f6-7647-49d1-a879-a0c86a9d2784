import { BadRequestException, Injectable } from '@nestjs/common';
import { ClassConstructor, plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { SecureMailBaseDto } from '../dtos/base.dto';
import { UserLanguage } from '@enums';

@Injectable()
export abstract class SecureMailBaseService<Dto extends SecureMailBaseDto> {
  abstract readonly requestBodyDto: ClassConstructor<Dto>;
  abstract readonly fileSystemName: string;

  async initiate(userLanguage: UserLanguage): Promise<any> {}

  async validateRequestBody(body: Dto) {
    const classDto = plainToInstance(this.requestBodyDto, body);
    const errors = await validate(classDto);
    if (errors.length > 0) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Validation failed',
        errors: errors.map((err) => ({
          property: err.property,
          constraints: err.constraints,
        })),
      });
    }

    return classDto;
  }

  getPayload(body: Dto) {
    return {
      type: body.type,
      fileSystemName: this.fileSystemName,
      freeFormatText: this.buildFreeText(body),
    };
  }

  buildFreeText(body: Dto): string {
    const textArray = this.getText(body);
    textArray.push(`Message: ${body.message ?? ''}`);
    return textArray?.join(', ');
  }

  abstract getText(body: Dto): string[];
}
