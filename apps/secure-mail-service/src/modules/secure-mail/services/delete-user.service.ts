import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { DeleteUserDto } from '../dtos/delete-user.dto';

@Injectable()
export class DeleteUserService extends SecureMailBaseService<DeleteUserDto> {
  fileSystemName: string = 'SS';
  requestBodyDto: ClassConstructor<DeleteUserDto> = DeleteUserDto;

  getText(body: DeleteUserDto): string[] {
    return [`Portal Type: ${body.portalType}`, `User: ${body.user}`];
  }
}
