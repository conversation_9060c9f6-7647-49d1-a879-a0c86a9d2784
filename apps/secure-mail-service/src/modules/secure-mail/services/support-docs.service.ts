import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { SupportDocsDto } from '../dtos/support-docs.dto';

@Injectable()
export class SupportDocsService extends SecureMailBaseService<SupportDocsDto> {
  requestBodyDto: ClassConstructor<SupportDocsDto> = SupportDocsDto;
  fileSystemName: string = 'PDC';

  getText(body: SupportDocsDto): string[] {
    return [
      `Previous Request: ${body.prevRequest ? 'Yes' : 'No'}`,
      `Request Number: ${body.requestNumber || 'Not provided'}`,
    ];
  }
}
