import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { AddNewUserDto } from '../dtos/add-user.dto';
import { DynamicListsLookupService } from '@modules';
import { SecureMailDynamicListEnum, UserLanguage } from '@enums';

@Injectable()
export class AddNewUserService extends SecureMailBaseService<AddNewUserDto> {
  fileSystemName: string = 'CP';
  requestBodyDto: ClassConstructor<AddNewUserDto> = AddNewUserDto;

  constructor(protected readonly listsLookup: DynamicListsLookupService) {
    super();
  }

  async initiate(userLanguage: UserLanguage): Promise<any> {
    return {
      userPrivileges: this.listsLookup.getValues(
        SecureMailDynamicListEnum.USER_PRIVILEGES,
        userLanguage,
      ),
      portalTypes: this.listsLookup.getValues(
        SecureMailDynamicListEnum.PORTAL_TYPES,
        userLanguage,
      ),
    };
  }

  getText(body: AddNewUserDto): string[] {
    return [
      `Arabic Full Name: ${body.fullNameAr}`,
      `English Full Name: ${body.fullNameEn}`,
      `Mobile Number: ${body.mobileNumber}`,
      `Privilege: ${body.privilege}`,
      `Portal Type: ${body.portalType}`,
      `Token Type: ${body.tokenType}`,
      `Has Delegation: ${body.hasDelegation}`,
    ];
  }
}
