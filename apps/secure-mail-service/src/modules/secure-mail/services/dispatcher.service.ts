import { SecureMailTypesEnum } from '@enums';
import { BadRequestException, Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { CpsChargeBackService } from './cps-charge-back.service';
import { AddNewUserService } from './add-user.service';
import { DeleteUserService } from './delete-user.service';
import { StopPaymentsService } from './stop-payments.service';
import { ChangeUserPrivilegeService } from './change-user-privilege.service';
import { CancelSecureMailService } from './cancel-securemail.service';
import { AddNewUserPrivilegeService } from './add-user-permission.service';
import { AssignNewProductService } from './assign-new-product.service';
import { CancelBankDraftService } from './cancel-bank-draft.service';
import { CpsAmendServiceService } from './cps-amend-service.service';
import { SupportDocsService } from './support-docs.service';
import { CpsAddAmendService } from './cps-add-amend.service';

@Injectable()
export class SecureMailDispatcherService {
  private readonly services: Record<
    SecureMailTypesEnum,
    SecureMailBaseService<any>
  >;

  constructor(
    private readonly cpsChargeBackService: CpsChargeBackService,
    private readonly addNewUserService: AddNewUserService,
    private readonly deleteUserService: DeleteUserService,
    private readonly stopPaymentsService: StopPaymentsService,
    private readonly changeUserPrivilegeService: ChangeUserPrivilegeService,
    private readonly cancelSecureMailService: CancelSecureMailService,
    private readonly addNewUserPrivilegeService: AddNewUserPrivilegeService,
    private readonly assignNewProductService: AssignNewProductService,
    private readonly cancelBankDraftService: CancelBankDraftService,
    private readonly cpsAmendServiceService: CpsAmendServiceService,
    private readonly supportDocsService: SupportDocsService,
    private readonly cpsAddAmendService: CpsAddAmendService,
  ) {
    this.services = {
      [SecureMailTypesEnum.CPS_CHARGE_BACK_REQUEST]: this.cpsChargeBackService,
      [SecureMailTypesEnum.ADD_NEW_USER]: this.addNewUserService,
      [SecureMailTypesEnum.DELETE_USERS]: this.deleteUserService,
      [SecureMailTypesEnum.STOP_MI_PAYMENTS]: this.stopPaymentsService,
      [SecureMailTypesEnum.CHANGE_USER_PRIVILEGES]:
        this.changeUserPrivilegeService,
      [SecureMailTypesEnum.CANCEL_SECURE_EMAIL]: this.cancelSecureMailService,
      [SecureMailTypesEnum.ADD_NEW_PERMISSION]: this.addNewUserPrivilegeService,
      [SecureMailTypesEnum.ASSIGN_ACCOUNT]: this.assignNewProductService,
      [SecureMailTypesEnum.CANCEL_BANK_DRAFT]: this.cancelBankDraftService,
      [SecureMailTypesEnum.CPS_AMEND_ADD_SERVICE]: this.cpsAmendServiceService,
      [SecureMailTypesEnum.SUPPORTING_DOCUMENTS]: this.supportDocsService,
    };
  }

  getService(type: SecureMailTypesEnum): SecureMailBaseService<any> {
    const service = this.services[type];
    if (!service) {
      throw new BadRequestException(`No service found`);
    }
    return service;
  }
}
