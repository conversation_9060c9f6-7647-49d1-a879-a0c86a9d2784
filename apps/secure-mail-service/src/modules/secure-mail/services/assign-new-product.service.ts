import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { AssignNewProductDto } from '../dtos/assign-new-product.dto';

@Injectable()
export class AssignNewProductService extends SecureMailBaseService<AssignNewProductDto> {
  fileSystemName: string = 'CRDST';
  requestBodyDto: ClassConstructor<AssignNewProductDto> = AssignNewProductDto;

  getText(body: AssignNewProductDto): string[] {
    return [
      `Product Type: ${body.productType}`,
      `Account Number: ${body.accountNumber}`,
      `User: ${body.user}`,
      `Portal Type: ${body.portalType}`,
    ];
  }
}
