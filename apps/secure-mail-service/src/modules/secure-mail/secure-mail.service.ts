import { Injectable } from '@nestjs/common';
import { FccClientService, I18nLookupService, RedisTnxService } from '@modules';
import { CurrentUser } from '@types';
import { FccRequestsEnum, UserLanguage } from '@enums';
import { v4 as UUIDV4 } from 'uuid';
import {
  getSecureMailReauthInputs,
  getTransactionStatusByCode,
  getTransactionSubStatusByCode,
} from '@helpers';
import { SecureMailDispatcherService } from './services/dispatcher.service';
import { SecureMailBaseDto, SecureMailInitiateDto } from './dtos/base.dto';
import { FccSEGenericTnxResponseDto } from '@dtos';

@Injectable()
export class SecureMailService {
  constructor(
    private readonly clientService: FccClientService,
    private readonly tnxService: RedisTnxService,
    private readonly i18n: I18nLookupService,

    private readonly dispatcher: SecureMailDispatcherService,
  ) {}

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const idempotencyKey = UUIDV4();
    const secureMailTxRecord =
      await this.clientService.call<FccSEGenericTnxResponseDto>({
        requestId: FccRequestsEnum.SECURE_MAIL_INITIATE,
        user,
        options: {
          idempotencyKey,
        },
      });

    await this.tnxService.setTnx(user, 'SECURE_MAIL', {
      ...secureMailTxRecord,
      idempotencyKey,
    });

    let initiateResults = {};

    if (body.type) {
      const service = this.dispatcher.getService(body.type);
      initiateResults = await service.initiate(userLanguage);
    }

    return {
      refId: secureMailTxRecord.refId,
      tnxId: secureMailTxRecord.tnxId,
      reauth: getSecureMailReauthInputs(secureMailTxRecord),
      ...initiateResults,
    };
  }

  async submit(
    body: SecureMailBaseDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const service = this.dispatcher.getService(body.type);

    await service.validateRequestBody(body);
    const payload = service.getPayload(body);

    const { idempotencyKey, tnxId, refId } = await this.tnxService.getTnx<any>(
      user,
      'SECURE_MAIL',
    );
    const transaction =
      await this.clientService.call<FccSEGenericTnxResponseDto>({
        requestId: FccRequestsEnum.SECURE_MAIL_SUBMIT,
        payload: {
          tnxId,
          refId,
          ...payload,
        },
        user,
        options: {
          idempotencyKey,
        },
      });
    const status = getTransactionStatusByCode(transaction.tnxStatCode);
    const subStatus = getTransactionSubStatusByCode(transaction.subTnxStatCode);

    return {
      refId: transaction.refId,
      status,
      subStatus,
      message: this.i18n.translate(
        `secureMail.submit.${subStatus}`,
        userLanguage,
      ),
    };
  }
}
