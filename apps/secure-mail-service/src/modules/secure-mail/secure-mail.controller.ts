import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { SecureMailService } from '@/modules/secure-mail/secure-mail.service';
import { UserLanguage } from '@enums';
import { SecureMailInitiateDto } from './dtos/base.dto';

@Controller('requests')
@UseGuards(JwtAuthGuard)
export class SecureMailController {
  constructor(private readonly secureMailService: SecureMailService) {}

  @Post('initiate')
  async initiate(
    @Body() body: SecureMailInitiateDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.secureMailService.initiate(body, currentUser, userLanguage);
  }

  @Post('submit')
  async submit(
    @Body() body: any,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.secureMailService.submit(body, currentUser, userLanguage);
  }
}
