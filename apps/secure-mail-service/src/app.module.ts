// app.module.ts
import { Module } from '@nestjs/common';
import {
  ApiAdapterClient,
  GlobalConfigModule,
  JwtAuthModule,
  LookupEntity,
  LookupsModule,
  RedisModule,
} from '@modules';
import defaults from './config/defaults';
import { SecureMailModule } from '@/modules/secure-mail/secure-mail.module';
import { SecureMailDynamicListEnum } from '@enums';

@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    RedisModule.forRootAsync(),
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
      {
        name: LookupEntity.CONTENT_LIST,
        filter: [
          SecureMailDynamicListEnum.PORTAL_TYPES,
          SecureMailDynamicListEnum.USER_PRIVILEGES,
        ],
      },
    ]),
    ApiAdapterClient,
    JwtAuthModule.forRoot(),
    SecureMailModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
